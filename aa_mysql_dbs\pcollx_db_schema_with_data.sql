-- =====================================================
-- PCOLLX DATABASE SCHEMA WITH SAMPLE DATA
-- Price Collection System Database
-- Generated on: 2025-08-09
-- Database: pcollx_db
-- Note: No foreign key constraints as requested
-- =====================================================

-- Drop database if exists and create new
DROP DATABASE IF EXISTS `pcollx_db`;
CREATE DATABASE `pcollx_db` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `pcollx_db`;

-- =====================================================
-- GEOGRAPHIC REFERENCE TABLES
-- =====================================================

-- Countries table
CREATE TABLE `geo_countries` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `code` varchar(10) DEFAULT NULL,
  `currency` varchar(10) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Provinces table
CREATE TABLE `geo_provinces` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `country_id` int(11) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `code` varchar(10) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Districts table
CREATE TABLE `geo_districts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `province_id` int(11) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `code` varchar(10) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- USER AND ORGANIZATION MANAGEMENT
-- =====================================================

-- System users table
CREATE TABLE `dakoii_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(100) NOT NULL,
  `email` varchar(255) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `first_name` varchar(100) DEFAULT NULL,
  `last_name` varchar(100) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `status` enum('active','inactive','suspended') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Organization table
CREATE TABLE `dakoii_org` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `type` varchar(100) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `contact_person` varchar(255) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Application users table
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(100) NOT NULL,
  `email` varchar(255) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `first_name` varchar(100) DEFAULT NULL,
  `last_name` varchar(100) DEFAULT NULL,
  `role` varchar(50) DEFAULT NULL,
  `organization_id` int(11) DEFAULT NULL,
  `district_id` int(11) DEFAULT NULL,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- GOODS AND PRODUCTS MANAGEMENT
-- =====================================================

-- Goods groups table
CREATE TABLE `goods_groups` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `group_name` varchar(255) NOT NULL,
  `group_code` varchar(50) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `parent_group_id` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Goods brands table
CREATE TABLE `goods_brands` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `brand_name` varchar(255) NOT NULL,
  `brand_code` varchar(50) DEFAULT NULL,
  `manufacturer` varchar(255) DEFAULT NULL,
  `country_of_origin` varchar(100) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Goods items table
CREATE TABLE `goods_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `item_name` varchar(255) NOT NULL,
  `item_code` varchar(50) DEFAULT NULL,
  `group_id` int(11) DEFAULT NULL,
  `brand_id` int(11) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `unit_of_measure` varchar(50) DEFAULT NULL,
  `standard_size` varchar(100) DEFAULT NULL,
  `barcode` varchar(100) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `item_code` (`item_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- BUSINESS ENTITIES AND LOCATIONS
-- =====================================================

-- Business entities table
CREATE TABLE `business_entities` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `business_name` varchar(255) NOT NULL,
  `business_type` enum('supermarket','retail_store','market','wholesaler','distributor','other') DEFAULT NULL,
  `registration_number` varchar(100) DEFAULT NULL,
  `contact_person` varchar(255) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `website` varchar(255) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Business locations table
CREATE TABLE `business_locations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `business_entity_id` int(11) NOT NULL,
  `location_name` varchar(255) NOT NULL,
  `location_code` varchar(50) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `district_id` int(11) DEFAULT NULL,
  `latitude` decimal(10,8) DEFAULT NULL,
  `longitude` decimal(11,8) DEFAULT NULL,
  `contact_person` varchar(255) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `operating_hours` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- PRICE COLLECTION ACTIVITIES
-- =====================================================

-- Activities table
CREATE TABLE `activities` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `activity_name` varchar(255) NOT NULL,
  `activity_code` varchar(50) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `frequency` enum('daily','weekly','monthly','quarterly','annual') DEFAULT NULL,
  `status` enum('planned','active','completed','cancelled','suspended') DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `activity_code` (`activity_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Activity users table
CREATE TABLE `activity_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `activity_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `role` enum('coordinator','collector','supervisor','analyst') DEFAULT NULL,
  `assigned_date` date DEFAULT NULL,
  `status` enum('assigned','active','completed','removed') DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Activity business locations table
CREATE TABLE `activity_business_locations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `activity_id` int(11) NOT NULL,
  `business_location_id` int(11) NOT NULL,
  `assigned_collector_id` int(11) DEFAULT NULL,
  `collection_frequency` enum('daily','weekly','monthly') DEFAULT NULL,
  `status` enum('assigned','active','completed','suspended') DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- PRICE DATA COLLECTION
-- =====================================================

-- Price data table
CREATE TABLE `price_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `activity_id` int(11) NOT NULL,
  `business_location_id` int(11) NOT NULL,
  `goods_item_id` int(11) NOT NULL,
  `collector_id` int(11) NOT NULL,
  `collection_date` date NOT NULL,
  `collection_time` time DEFAULT NULL,
  `price` decimal(10,2) NOT NULL,
  `currency` varchar(10) DEFAULT 'PGK',
  `unit_size` varchar(100) DEFAULT NULL,
  `availability_status` enum('available','limited','out_of_stock','discontinued') DEFAULT 'available',
  `quality_grade` enum('premium','standard','economy','poor') DEFAULT NULL,
  `promotion_type` varchar(100) DEFAULT NULL,
  `promotion_discount` decimal(5,2) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `verified_by` int(11) DEFAULT NULL,
  `verification_date` date DEFAULT NULL,
  `status` enum('draft','submitted','verified','rejected') DEFAULT 'draft',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_price_data_date` (`collection_date`),
  KEY `idx_price_data_location` (`business_location_id`),
  KEY `idx_price_data_item` (`goods_item_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Activity price collection data table (summary/aggregated data)
CREATE TABLE `activity_price_collection_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `activity_id` int(11) NOT NULL,
  `goods_item_id` int(11) NOT NULL,
  `collection_period` date NOT NULL,
  `total_locations_surveyed` int(11) DEFAULT NULL,
  `locations_with_data` int(11) DEFAULT NULL,
  `average_price` decimal(10,2) DEFAULT NULL,
  `minimum_price` decimal(10,2) DEFAULT NULL,
  `maximum_price` decimal(10,2) DEFAULT NULL,
  `price_variance` decimal(10,4) DEFAULT NULL,
  `availability_percentage` decimal(5,2) DEFAULT NULL,
  `total_records` int(11) DEFAULT NULL,
  `verified_records` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_activity_price_period` (`collection_period`),
  KEY `idx_activity_price_item` (`goods_item_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- WORKPLAN MANAGEMENT
-- =====================================================

-- Workplans table
CREATE TABLE `workplans` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `workplan_name` varchar(255) NOT NULL,
  `workplan_code` varchar(50) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `budget_allocated` decimal(15,2) DEFAULT NULL,
  `budget_spent` decimal(15,2) DEFAULT NULL,
  `responsible_officer_id` int(11) DEFAULT NULL,
  `status` enum('draft','approved','active','completed','cancelled') DEFAULT 'draft',
  `created_by` int(11) DEFAULT NULL,
  `approved_by` int(11) DEFAULT NULL,
  `approved_date` date DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `workplan_code` (`workplan_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- SAMPLE DATA INSERTION
-- =====================================================

-- Insert sample countries
INSERT INTO `geo_countries` (`name`, `code`, `currency`) VALUES
('Papua New Guinea', 'PG', 'PGK'),
('Australia', 'AU', 'AUD'),
('Solomon Islands', 'SB', 'SBD'),
('Vanuatu', 'VU', 'VUV'),
('Fiji', 'FJ', 'FJD');

-- Insert sample provinces
INSERT INTO `geo_provinces` (`country_id`, `name`, `code`) VALUES
(1, 'National Capital District', 'NCD'),
(1, 'Central Province', 'CP'),
(1, 'Western Province', 'WP'),
(1, 'Gulf Province', 'GP'),
(1, 'Morobe Province', 'MP'),
(1, 'Eastern Highlands Province', 'EHP'),
(1, 'Western Highlands Province', 'WHP'),
(1, 'Southern Highlands Province', 'SHP'),
(1, 'Enga Province', 'EP'),
(1, 'Chimbu Province', 'CHP');

-- Insert sample districts
INSERT INTO `geo_districts` (`province_id`, `name`, `code`) VALUES
(1, 'Port Moresby', 'POM'),
(1, 'Moresby Northeast', 'MNE'),
(1, 'Moresby Northwest', 'MNW'),
(1, 'Moresby South', 'MS'),
(2, 'Abau', 'ABA'),
(2, 'Goilala', 'GOI'),
(2, 'Kairuku-Hiri', 'KH'),
(2, 'Rigo', 'RIG'),
(3, 'Daru', 'DAR'),
(3, 'Kiunga', 'KIU');

-- Insert sample system users
INSERT INTO `dakoii_users` (`username`, `email`, `password_hash`, `first_name`, `last_name`, `phone`, `status`) VALUES
('admin', '<EMAIL>', '$2y$10$example_hash_1', 'System', 'Administrator', '+************', 'active'),
('coordinator1', '<EMAIL>', '$2y$10$example_hash_2', 'John', 'Coordinator', '+************', 'active'),
('collector1', '<EMAIL>', '$2y$10$example_hash_3', 'Mary', 'Collector', '+************', 'active'),
('collector2', '<EMAIL>', '$2y$10$example_hash_4', 'Peter', 'Fieldworker', '+************', 'active'),
('supervisor1', '<EMAIL>', '$2y$10$example_hash_5', 'Sarah', 'Supervisor', '+************', 'active');

-- Insert sample organizations
INSERT INTO `dakoii_org` (`name`, `type`, `address`, `contact_person`, `phone`, `email`) VALUES
('National Statistical Office', 'Government', 'Waigani, Port Moresby', 'Chief Statistician', '+************', '<EMAIL>'),
('Department of Treasury', 'Government', 'Waigani, Port Moresby', 'Secretary Treasury', '+************', '<EMAIL>'),
('Bank of Papua New Guinea', 'Central Bank', 'Port Moresby', 'Governor BPNG', '+************', '<EMAIL>'),
('PNG Chamber of Commerce', 'Private Sector', 'Port Moresby', 'Executive Director', '+************', '<EMAIL>'),
('Consumer Council of PNG', 'Regulatory', 'Port Moresby', 'Executive Officer', '+************', '<EMAIL>');

-- Insert sample application users
INSERT INTO `users` (`username`, `email`, `password_hash`, `first_name`, `last_name`, `role`, `organization_id`, `district_id`, `status`) VALUES
('price_coordinator', '<EMAIL>', '$2y$10$example_hash_6', 'James', 'Price', 'Price Coordinator', 1, 1, 'active'),
('field_collector1', '<EMAIL>', '$2y$10$example_hash_7', 'Grace', 'Market', 'Field Collector', 1, 1, 'active'),
('field_collector2', '<EMAIL>', '$2y$10$example_hash_8', 'David', 'Survey', 'Field Collector', 1, 2, 'active'),
('data_analyst', '<EMAIL>', '$2y$10$example_hash_9', 'Ruth', 'Data', 'Data Analyst', 1, 1, 'active'),
('supervisor_central', '<EMAIL>', '$2y$10$example_hash_10', 'Michael', 'Central', 'Regional Supervisor', 1, 2, 'active');

-- Insert sample goods groups
INSERT INTO `goods_groups` (`group_name`, `group_code`, `description`, `parent_group_id`) VALUES
('Food and Beverages', 'FB', 'All food and beverage items', NULL),
('Fresh Food', 'FF', 'Fresh food items including fruits, vegetables, meat', 1),
('Processed Food', 'PF', 'Processed and packaged food items', 1),
('Beverages', 'BV', 'All types of beverages', 1),
('Household Items', 'HH', 'Household goods and supplies', NULL),
('Personal Care', 'PC', 'Personal care and hygiene products', NULL),
('Clothing', 'CL', 'Clothing and footwear', NULL),
('Electronics', 'EL', 'Electronic goods and appliances', NULL),
('Fruits', 'FR', 'Fresh fruits', 2),
('Vegetables', 'VG', 'Fresh vegetables', 2),
('Meat and Fish', 'MF', 'Fresh meat and fish', 2),
('Rice and Grains', 'RG', 'Rice and grain products', 3),
('Canned Goods', 'CG', 'Canned and preserved foods', 3),
('Soft Drinks', 'SD', 'Carbonated and soft drinks', 4),
('Alcoholic Beverages', 'AB', 'Beer, wine, and spirits', 4);

-- Insert sample goods brands
INSERT INTO `goods_brands` (`brand_name`, `brand_code`, `manufacturer`, `country_of_origin`, `description`) VALUES
('Trukai', 'TRU', 'Trukai Industries', 'Papua New Guinea', 'Local rice brand'),
('Ramu', 'RAM', 'Ramu Sugar', 'Papua New Guinea', 'Local sugar producer'),
('Coca-Cola', 'COC', 'Coca-Cola Company', 'USA', 'International beverage brand'),
('Maggi', 'MAG', 'Nestle', 'Switzerland', 'Food seasoning and noodles'),
('Colgate', 'COL', 'Colgate-Palmolive', 'USA', 'Personal care products'),
('Sunlight', 'SUN', 'Unilever', 'Netherlands', 'Household cleaning products'),
('Taiyo', 'TAI', 'Taiyo Fishery', 'Japan', 'Canned fish products'),
('SP Brewery', 'SPB', 'SP Brewery', 'Papua New Guinea', 'Local beer producer'),
('Paradise', 'PAR', 'Paradise Foods', 'Papua New Guinea', 'Local food manufacturer'),
('Lae Biscuit', 'LAE', 'Lae Biscuit Company', 'Papua New Guinea', 'Local biscuit manufacturer');

-- Insert sample goods items
INSERT INTO `goods_items` (`item_name`, `item_code`, `group_id`, `brand_id`, `description`, `unit_of_measure`, `standard_size`, `barcode`, `is_active`) VALUES
('White Rice 10kg', 'RICE10', 12, 1, 'Trukai white rice 10kg bag', 'bag', '10kg', '9300000000001', 1),
('White Sugar 1kg', 'SUGAR1', 12, 2, 'Ramu white sugar 1kg pack', 'pack', '1kg', '9300000000002', 1),
('Coca-Cola 375ml', 'COKE375', 14, 3, 'Coca-Cola soft drink 375ml can', 'can', '375ml', '9300000000003', 1),
('Maggi Noodles 2min', 'NOODLE2', 13, 4, 'Maggi 2-minute noodles', 'pack', '85g', '9300000000004', 1),
('Colgate Toothpaste', 'TOOTH100', 6, 5, 'Colgate toothpaste regular', 'tube', '100ml', '9300000000005', 1),
('Sunlight Soap Bar', 'SOAP150', 5, 6, 'Sunlight laundry soap bar', 'bar', '150g', '9300000000006', 1),
('Taiyo Tuna Can', 'TUNA185', 13, 7, 'Taiyo tuna in oil 185g can', 'can', '185g', '9300000000007', 1),
('SP Lager Beer', 'BEER375', 15, 8, 'SP Lager beer 375ml bottle', 'bottle', '375ml', '9300000000008', 1),
('Paradise Corned Beef', 'BEEF340', 13, 9, 'Paradise corned beef 340g can', 'can', '340g', '9300000000009', 1),
('Lae Biscuits Assorted', 'BISC200', 13, 10, 'Lae biscuits assorted 200g pack', 'pack', '200g', '9300000000010', 1),
('Bananas Local', 'BANANA', 9, NULL, 'Local bananas', 'kg', 'per kg', NULL, 1),
('Sweet Potato', 'SWEETPOT', 10, NULL, 'Local sweet potato', 'kg', 'per kg', NULL, 1),
('Fresh Fish Barramundi', 'FISHBAR', 11, NULL, 'Fresh barramundi fish', 'kg', 'per kg', NULL, 1),
('Chicken Whole', 'CHICKWHL', 11, NULL, 'Whole chicken fresh', 'kg', 'per kg', NULL, 1),
('Bread White Loaf', 'BREADWHT', 3, NULL, 'White bread loaf', 'loaf', '600g', NULL, 1);

-- Insert sample business entities
INSERT INTO `business_entities` (`business_name`, `business_type`, `registration_number`, `contact_person`, `phone`, `email`, `description`, `is_active`) VALUES
('Stop N Shop', 'supermarket', 'REG001', 'John Manager', '+************', '<EMAIL>', 'Major supermarket chain', 1),
('RH Hypermarket', 'supermarket', 'REG002', 'Mary Store', '+************', '<EMAIL>', 'Large hypermarket', 1),
('Boroko Foodworld', 'supermarket', 'REG003', 'Peter Food', '+************', '<EMAIL>', 'Local supermarket', 1),
('Gordons Market', 'market', 'REG004', 'Grace Market', '+************', NULL, 'Traditional fresh market', 1),
('Koki Market', 'market', 'REG005', 'David Vendor', '+************', NULL, 'Local fresh produce market', 1),
('Brian Bell Retail', 'retail_store', 'REG006', 'Sarah Bell', '+************', '<EMAIL>', 'General retail store', 1),
('Ela Motors Store', 'retail_store', 'REG007', 'Michael Auto', '+************', '<EMAIL>', 'Automotive and general goods', 1),
('City Pharmacy', 'retail_store', 'REG008', 'Ruth Pharm', '+************', '<EMAIL>', 'Pharmacy and health products', 1),
('Steamships Trading', 'wholesaler', 'REG009', 'James Trade', '+************', '<EMAIL>', 'Wholesale distributor', 1),
('Collins Foods Distribution', 'distributor', 'REG010', 'Lisa Collins', '+************', '<EMAIL>', 'Food distributor', 1);

-- Insert sample business locations
INSERT INTO `business_locations` (`business_entity_id`, `location_name`, `location_code`, `address`, `district_id`, `latitude`, `longitude`, `contact_person`, `phone`, `operating_hours`, `is_active`) VALUES
(1, 'Stop N Shop Waigani', 'SNS_WAI', 'Waigani Shopping Centre', 1, -9.4438, 147.1803, 'Store Manager', '+************', '8:00 AM - 8:00 PM', 1),
(1, 'Stop N Shop Boroko', 'SNS_BOR', 'Boroko Shopping Centre', 1, -9.4647, 147.1925, 'Assistant Manager', '+************', '8:00 AM - 8:00 PM', 1),
(2, 'RH Hypermarket Gerehu', 'RH_GER', 'Gerehu Stage 6', 1, -9.4500, 147.1600, 'Floor Manager', '+************', '7:00 AM - 9:00 PM', 1),
(3, 'Boroko Foodworld Main', 'BF_MAIN', 'Boroko Main Street', 1, -9.4650, 147.1930, 'Store Supervisor', '+************', '7:30 AM - 7:30 PM', 1),
(4, 'Gordons Market Main', 'GM_MAIN', 'Gordons Market Area', 1, -9.4600, 147.1850, 'Market Coordinator', '+************', '5:00 AM - 6:00 PM', 1),
(5, 'Koki Market Fresh', 'KM_FRESH', 'Koki Market Complex', 1, -9.4700, 147.1800, 'Market Leader', '+************', '5:00 AM - 5:00 PM', 1),
(6, 'Brian Bell Downtown', 'BB_DOWN', 'Downtown Port Moresby', 1, -9.4647, 147.1925, 'Retail Manager', '+************', '8:30 AM - 5:30 PM', 1),
(7, 'Ela Motors Jacksons', 'EM_JACK', 'Jacksons Airport Road', 1, -9.4400, 147.2200, 'Store Manager', '+************', '8:00 AM - 6:00 PM', 1),
(8, 'City Pharmacy Waigani', 'CP_WAI', 'Waigani Medical Centre', 1, -9.4440, 147.1800, 'Pharmacist', '+************', '8:00 AM - 6:00 PM', 1),
(9, 'Steamships Warehouse', 'ST_WARE', 'Port Moresby Industrial', 1, -9.4800, 147.1700, 'Warehouse Manager', '+************', '7:00 AM - 5:00 PM', 1);

-- Insert sample activities
INSERT INTO `activities` (`activity_name`, `activity_code`, `description`, `start_date`, `end_date`, `frequency`, `status`, `created_by`) VALUES
('Monthly Consumer Price Survey', 'CPS_2024', 'Monthly collection of consumer prices for CPI calculation', '2024-01-01', '2024-12-31', 'monthly', 'active', 1),
('Weekly Fresh Market Survey', 'FMS_2024', 'Weekly price monitoring of fresh produce markets', '2024-01-01', '2024-12-31', 'weekly', 'active', 1),
('Quarterly Retail Price Survey', 'RPS_Q2024', 'Quarterly comprehensive retail price survey', '2024-01-01', '2024-12-31', 'quarterly', 'active', 1),
('Special Holiday Price Monitoring', 'HPM_2024', 'Price monitoring during holiday seasons', '2024-12-01', '2024-12-31', 'daily', 'planned', 1),
('Annual Household Expenditure Survey', 'HES_2024', 'Annual survey for household expenditure patterns', '2024-06-01', '2024-08-31', 'annual', 'completed', 1);

-- Insert sample activity users
INSERT INTO `activity_users` (`activity_id`, `user_id`, `role`, `assigned_date`, `status`) VALUES
(1, 1, 'coordinator', '2024-01-01', 'active'),
(1, 2, 'collector', '2024-01-01', 'active'),
(1, 3, 'collector', '2024-01-01', 'active'),
(1, 5, 'supervisor', '2024-01-01', 'active'),
(2, 2, 'coordinator', '2024-01-01', 'active'),
(2, 3, 'collector', '2024-01-01', 'active'),
(3, 1, 'coordinator', '2024-01-01', 'active'),
(3, 4, 'analyst', '2024-01-01', 'active'),
(4, 2, 'collector', '2024-12-01', 'assigned'),
(5, 1, 'coordinator', '2024-06-01', 'completed');

-- Insert sample activity business locations
INSERT INTO `activity_business_locations` (`activity_id`, `business_location_id`, `assigned_collector_id`, `collection_frequency`, `status`) VALUES
(1, 1, 2, 'monthly', 'active'),
(1, 2, 2, 'monthly', 'active'),
(1, 3, 3, 'monthly', 'active'),
(1, 4, 3, 'monthly', 'active'),
(2, 5, 2, 'weekly', 'active'),
(2, 6, 3, 'weekly', 'active'),
(3, 1, 2, 'monthly', 'active'),
(3, 3, 3, 'monthly', 'active'),
(3, 7, 2, 'monthly', 'active'),
(4, 1, 2, 'daily', 'assigned');

-- Insert sample price data
INSERT INTO `price_data` (`activity_id`, `business_location_id`, `goods_item_id`, `collector_id`, `collection_date`, `collection_time`, `price`, `currency`, `unit_size`, `availability_status`, `quality_grade`, `notes`, `status`) VALUES
(1, 1, 1, 2, '2024-08-01', '10:30:00', 45.50, 'PGK', '10kg', 'available', 'standard', 'Regular stock level', 'verified'),
(1, 1, 2, 2, '2024-08-01', '10:35:00', 4.20, 'PGK', '1kg', 'available', 'standard', 'Good quality', 'verified'),
(1, 1, 3, 2, '2024-08-01', '10:40:00', 2.50, 'PGK', '375ml', 'available', 'standard', 'Cold stock available', 'verified'),
(1, 2, 1, 2, '2024-08-01', '14:15:00', 46.00, 'PGK', '10kg', 'available', 'standard', 'Slightly higher price', 'verified'),
(1, 2, 2, 2, '2024-08-01', '14:20:00', 4.15, 'PGK', '1kg', 'available', 'standard', 'Competitive price', 'verified'),
(1, 3, 1, 3, '2024-08-01', '11:00:00', 44.80, 'PGK', '10kg', 'available', 'standard', 'Bulk discount available', 'verified'),
(2, 5, 11, 2, '2024-08-05', '08:00:00', 3.50, 'PGK', '1kg', 'available', 'premium', 'Fresh morning stock', 'verified'),
(2, 5, 12, 2, '2024-08-05', '08:05:00', 2.80, 'PGK', '1kg', 'available', 'standard', 'Local variety', 'verified'),
(2, 6, 11, 3, '2024-08-05', '09:30:00', 3.20, 'PGK', '1kg', 'available', 'standard', 'Good quality bananas', 'verified'),
(2, 6, 13, 3, '2024-08-05', '09:35:00', 18.50, 'PGK', '1kg', 'limited', 'premium', 'Limited fresh stock', 'verified');

-- Insert sample workplans
INSERT INTO `workplans` (`workplan_name`, `workplan_code`, `description`, `start_date`, `end_date`, `budget_allocated`, `budget_spent`, `responsible_officer_id`, `status`, `created_by`, `approved_by`, `approved_date`) VALUES
('2024 Price Collection Program', 'PCP_2024', 'Annual price collection program for CPI and economic indicators', '2024-01-01', '2024-12-31', 150000.00, 95000.00, 1, 'active', 1, 1, '2023-12-15'),
('Market Infrastructure Survey', 'MIS_2024', 'Survey of market infrastructure and facilities', '2024-03-01', '2024-06-30', 75000.00, 45000.00, 2, 'active', 1, 1, '2024-02-20'),
('Digital Price Collection System', 'DPCS_2024', 'Implementation of digital price collection tools', '2024-07-01', '2024-12-31', 200000.00, 120000.00, 1, 'active', 1, 1, '2024-06-15'),
('Training and Capacity Building', 'TCB_2024', 'Training program for price collectors and supervisors', '2024-02-01', '2024-11-30', 50000.00, 30000.00, 5, 'active', 1, 1, '2024-01-25'),
('Quality Assurance Program', 'QAP_2024', 'Quality assurance and data validation program', '2024-01-01', '2024-12-31', 25000.00, 15000.00, 4, 'active', 1, 1, '2023-12-20');

-- =====================================================
-- END OF SCHEMA AND DATA
-- =====================================================

-- Summary of created tables:
-- 1. Geographic Reference Tables: 3 tables (geo_countries, geo_provinces, geo_districts)
-- 2. User Management Tables: 3 tables (dakoii_users, dakoii_org, users)
-- 3. Goods Management Tables: 3 tables (goods_groups, goods_brands, goods_items)
-- 4. Business Management Tables: 2 tables (business_entities, business_locations)
-- 5. Activity Management Tables: 3 tables (activities, activity_users, activity_business_locations)
-- 6. Price Data Tables: 2 tables (price_data, activity_price_collection_data)
-- 7. Workplan Management Tables: 1 table (workplans)

-- Total: 17 tables with comprehensive sample data
-- Database ready for price collection and monitoring system
