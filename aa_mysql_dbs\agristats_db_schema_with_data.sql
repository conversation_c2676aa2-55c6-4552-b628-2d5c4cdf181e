-- =====================================================
-- AGRISTATS DATABASE SCHEMA WITH SAMPLE DATA
-- Generated on: 2025-08-09
-- Database: agristats_db
-- Note: No foreign key constraints as requested
-- =====================================================

-- Drop database if exists and create new
DROP DATABASE IF EXISTS `agristats_db`;
CREATE DATABASE `agristats_db` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `agristats_db`;

-- =====================================================
-- ADMINISTRATIVE/REFERENCE TABLES
-- =====================================================

-- Countries table
CREATE TABLE `adx_country` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `code` varchar(10) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Provinces table
CREATE TABLE `adx_province` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `country_id` int(11) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `code` varchar(10) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Districts table
CREATE TABLE `adx_district` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `province_id` int(11) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `code` varchar(10) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Local Level Government table
CREATE TABLE `adx_llg` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `district_id` int(11) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `code` varchar(10) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Wards table
CREATE TABLE `adx_ward` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `llg_id` int(11) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `code` varchar(10) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Education levels table
CREATE TABLE `adx_education` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `level` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- AGRICULTURAL REFERENCE TABLES
-- =====================================================

-- Crops table
CREATE TABLE `adx_crops` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `scientific_name` varchar(255) DEFAULT NULL,
  `category` varchar(100) DEFAULT NULL,
  `variety` varchar(255) DEFAULT NULL,
  `season` varchar(50) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Livestock table
CREATE TABLE `adx_livestock` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `category` varchar(100) DEFAULT NULL,
  `breed` varchar(255) DEFAULT NULL,
  `purpose` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Fertilizers table
CREATE TABLE `adx_fertilizers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `type` varchar(100) DEFAULT NULL,
  `composition` text DEFAULT NULL,
  `application_method` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Pesticides table
CREATE TABLE `adx_pesticides` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `type` varchar(100) DEFAULT NULL,
  `active_ingredient` varchar(255) DEFAULT NULL,
  `target_pest` varchar(255) DEFAULT NULL,
  `application_method` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Infections/Diseases table
CREATE TABLE `adx_infections` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `type` varchar(100) DEFAULT NULL,
  `affected_crops` text DEFAULT NULL,
  `symptoms` text DEFAULT NULL,
  `treatment` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- USER AND ORGANIZATION MANAGEMENT
-- =====================================================

-- System users table
CREATE TABLE `dakoii_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(100) NOT NULL,
  `email` varchar(255) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `first_name` varchar(100) DEFAULT NULL,
  `last_name` varchar(100) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `status` enum('active','inactive','suspended') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Organization table
CREATE TABLE `dakoii_org` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `type` varchar(100) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `contact_person` varchar(255) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Application users table
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(100) NOT NULL,
  `email` varchar(255) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `first_name` varchar(100) DEFAULT NULL,
  `last_name` varchar(100) DEFAULT NULL,
  `role` varchar(50) DEFAULT NULL,
  `organization_id` int(11) DEFAULT NULL,
  `district_id` int(11) DEFAULT NULL,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- FARMER MANAGEMENT
-- =====================================================

-- Farmer information table
CREATE TABLE `farmer_information` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `farmer_code` varchar(50) DEFAULT NULL,
  `first_name` varchar(100) NOT NULL,
  `last_name` varchar(100) NOT NULL,
  `gender` enum('male','female','other') DEFAULT NULL,
  `date_of_birth` date DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `ward_id` int(11) DEFAULT NULL,
  `education_level_id` int(11) DEFAULT NULL,
  `farming_experience_years` int(11) DEFAULT NULL,
  `land_size_hectares` decimal(10,2) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `farmer_code` (`farmer_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Farmers children table
CREATE TABLE `farmers_children` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `farmer_id` int(11) NOT NULL,
  `child_name` varchar(255) NOT NULL,
  `gender` enum('male','female') DEFAULT NULL,
  `date_of_birth` date DEFAULT NULL,
  `education_status` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Field visits table
CREATE TABLE `field_visits` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `farmer_id` int(11) NOT NULL,
  `visitor_id` int(11) NOT NULL,
  `visit_date` date NOT NULL,
  `purpose` varchar(255) DEFAULT NULL,
  `observations` text DEFAULT NULL,
  `recommendations` text DEFAULT NULL,
  `follow_up_required` tinyint(1) DEFAULT 0,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- SAMPLE DATA INSERTION
-- =====================================================

-- Insert sample countries
INSERT INTO `adx_country` (`name`, `code`) VALUES
('Papua New Guinea', 'PG'),
('Australia', 'AU'),
('Solomon Islands', 'SB'),
('Vanuatu', 'VU'),
('Fiji', 'FJ');

-- Insert sample provinces
INSERT INTO `adx_province` (`country_id`, `name`, `code`) VALUES
(1, 'Western Province', 'WP'),
(1, 'Gulf Province', 'GP'),
(1, 'Central Province', 'CP'),
(1, 'National Capital District', 'NCD'),
(1, 'Morobe Province', 'MP'),
(1, 'Eastern Highlands Province', 'EHP'),
(1, 'Western Highlands Province', 'WHP'),
(1, 'Southern Highlands Province', 'SHP'),
(1, 'Enga Province', 'EP'),
(1, 'Chimbu Province', 'CHP');

-- Insert sample districts
INSERT INTO `adx_district` (`province_id`, `name`, `code`) VALUES
(1, 'Daru', 'DAR'),
(1, 'Kiunga', 'KIU'),
(1, 'North Fly', 'NF'),
(1, 'South Fly', 'SF'),
(2, 'Kerema', 'KER'),
(2, 'Kikori', 'KIK'),
(3, 'Abau', 'ABA'),
(3, 'Goilala', 'GOI'),
(3, 'Kairuku-Hiri', 'KH'),
(3, 'Rigo', 'RIG');

-- Insert sample education levels
INSERT INTO `adx_education` (`level`, `description`) VALUES
('No Formal Education', 'No formal schooling'),
('Primary Education', 'Elementary school level'),
('Secondary Education', 'High school level'),
('Vocational Training', 'Technical and vocational education'),
('Tertiary Education', 'University or college level'),
('Postgraduate', 'Masters or PhD level');

-- Insert sample crops
INSERT INTO `adx_crops` (`name`, `scientific_name`, `category`, `variety`, `season`) VALUES
('Sweet Potato', 'Ipomoea batatas', 'Root Crop', 'Highland', 'Year Round'),
('Taro', 'Colocasia esculenta', 'Root Crop', 'Wetland', 'Wet Season'),
('Banana', 'Musa spp.', 'Fruit', 'Cavendish', 'Year Round'),
('Coffee', 'Coffea arabica', 'Cash Crop', 'Arabica', 'Dry Season'),
('Cocoa', 'Theobroma cacao', 'Cash Crop', 'Trinitario', 'Year Round'),
('Coconut', 'Cocos nucifera', 'Tree Crop', 'Tall Variety', 'Year Round'),
('Sago', 'Metroxylon sagu', 'Staple', 'Traditional', 'Year Round'),
('Cassava', 'Manihot esculenta', 'Root Crop', 'Bitter', 'Dry Season');

-- Insert sample livestock
INSERT INTO `adx_livestock` (`name`, `category`, `breed`, `purpose`) VALUES
('Pig', 'Swine', 'Local Breed', 'Meat Production'),
('Chicken', 'Poultry', 'Village Chicken', 'Eggs and Meat'),
('Duck', 'Poultry', 'Muscovy', 'Meat Production'),
('Goat', 'Small Ruminant', 'Local Breed', 'Meat and Milk'),
('Cattle', 'Large Ruminant', 'Brahman Cross', 'Beef Production'),
('Fish', 'Aquaculture', 'Tilapia', 'Protein Source');

-- Insert sample fertilizers
INSERT INTO `adx_fertilizers` (`name`, `type`, `composition`, `application_method`) VALUES
('NPK 15-15-15', 'Compound', 'Nitrogen 15%, Phosphorus 15%, Potassium 15%', 'Soil Application'),
('Urea', 'Nitrogen', 'Nitrogen 46%', 'Soil Application'),
('Compost', 'Organic', 'Organic Matter 40-60%', 'Soil Incorporation'),
('Chicken Manure', 'Organic', 'Nitrogen 3%, Phosphorus 2%, Potassium 1%', 'Soil Application'),
('Superphosphate', 'Phosphorus', 'Phosphorus 20%', 'Soil Application');

-- Insert sample pesticides
INSERT INTO `adx_pesticides` (`name`, `type`, `active_ingredient`, `target_pest`, `application_method`) VALUES
('Malathion', 'Insecticide', 'Malathion 50%', 'Aphids, Thrips', 'Foliar Spray'),
('Roundup', 'Herbicide', 'Glyphosate 41%', 'Weeds', 'Foliar Spray'),
('Copper Fungicide', 'Fungicide', 'Copper Sulfate 25%', 'Fungal Diseases', 'Foliar Spray'),
('Neem Oil', 'Bio-pesticide', 'Azadirachtin 1%', 'Various Insects', 'Foliar Spray'),
('2,4-D', 'Herbicide', '2,4-D 80%', 'Broadleaf Weeds', 'Foliar Spray');

-- Insert sample infections/diseases
INSERT INTO `adx_infections` (`name`, `type`, `affected_crops`, `symptoms`, `treatment`) VALUES
('Black Sigatoka', 'Fungal', 'Banana', 'Black streaks on leaves', 'Fungicide application'),
('Coffee Berry Disease', 'Fungal', 'Coffee', 'Dark lesions on berries', 'Copper-based fungicides'),
('Taro Leaf Blight', 'Fungal', 'Taro', 'Brown spots on leaves', 'Resistant varieties'),
('Sweet Potato Weevil', 'Insect', 'Sweet Potato', 'Holes in tubers', 'Clean planting material'),
('Cocoa Pod Borer', 'Insect', 'Cocoa', 'Holes in pods', 'Integrated pest management');

-- Insert sample system users
INSERT INTO `dakoii_users` (`username`, `email`, `password_hash`, `first_name`, `last_name`, `phone`, `status`) VALUES
('admin', '<EMAIL>', '$2y$10$example_hash_1', 'System', 'Administrator', '+************', 'active'),
('jsmith', '<EMAIL>', '$2y$10$example_hash_2', 'John', 'Smith', '+************', 'active'),
('mwilson', '<EMAIL>', '$2y$10$example_hash_3', 'Mary', 'Wilson', '+************', 'active'),
('pkila', '<EMAIL>', '$2y$10$example_hash_4', 'Peter', 'Kila', '+************', 'active'),
('snamaliu', '<EMAIL>', '$2y$10$example_hash_5', 'Sarah', 'Namaliu', '+************', 'active');

-- Insert sample organizations
INSERT INTO `dakoii_org` (`name`, `type`, `address`, `contact_person`, `phone`, `email`) VALUES
('Department of Agriculture and Livestock', 'Government', 'Konedobu, Port Moresby', 'Secretary DAL', '+************', '<EMAIL>'),
('National Agricultural Research Institute', 'Research', 'Laloki, Central Province', 'Director NARI', '+************', '<EMAIL>'),
('Coffee Industry Corporation', 'Statutory Body', 'Goroka, Eastern Highlands', 'General Manager', '+************', '<EMAIL>'),
('Cocoa Board of PNG', 'Statutory Body', 'Rabaul, East New Britain', 'General Manager', '+************', '<EMAIL>'),
('Fresh Produce Development Agency', 'Statutory Body', 'Port Moresby, NCD', 'Chief Executive', '+************', '<EMAIL>');

-- Insert sample application users
INSERT INTO `users` (`username`, `email`, `password_hash`, `first_name`, `last_name`, `role`, `organization_id`, `district_id`, `status`) VALUES
('field_officer1', '<EMAIL>', '$2y$10$example_hash_6', 'James', 'Temu', 'Field Officer', 1, 1, 'active'),
('field_officer2', '<EMAIL>', '$2y$10$example_hash_7', 'Grace', 'Mendi', 'Field Officer', 1, 2, 'active'),
('researcher1', '<EMAIL>', '$2y$10$example_hash_8', 'David', 'Kerenga', 'Researcher', 2, 3, 'active'),
('extension1', '<EMAIL>', '$2y$10$example_hash_9', 'Ruth', 'Waigani', 'Extension Officer', 1, 4, 'active'),
('supervisor1', '<EMAIL>', '$2y$10$example_hash_10', 'Michael', 'Natera', 'Supervisor', 1, 5, 'active');

-- Insert sample farmers
INSERT INTO `farmer_information` (`farmer_code`, `first_name`, `last_name`, `gender`, `date_of_birth`, `phone`, `address`, `ward_id`, `education_level_id`, `farming_experience_years`, `land_size_hectares`) VALUES
('FRM001', 'Joseph', 'Kila', 'male', '1975-03-15', '+675 7123 4567', 'Daru Village, Western Province', 1, 2, 25, 2.5),
('FRM002', 'Mary', 'Temu', 'female', '1980-07-22', '+675 7234 5678', 'Kiunga Town, Western Province', 2, 3, 18, 1.8),
('FRM003', 'Peter', 'Namaliu', 'male', '1970-11-08', '+675 7345 6789', 'Kerema District, Gulf Province', 3, 2, 30, 3.2),
('FRM004', 'Grace', 'Mendi', 'female', '1985-05-12', '+675 7456 7890', 'Kikori Area, Gulf Province', 4, 4, 12, 1.5),
('FRM005', 'David', 'Waigani', 'male', '1978-09-30', '+675 7567 8901', 'Abau District, Central Province', 5, 3, 20, 2.8);

-- =====================================================
-- FARM BLOCK MANAGEMENT TABLES
-- =====================================================

-- Crops farm blocks table
CREATE TABLE `crops_farm_blocks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `farmer_id` int(11) NOT NULL,
  `block_name` varchar(255) NOT NULL,
  `block_code` varchar(50) DEFAULT NULL,
  `size_hectares` decimal(10,2) DEFAULT NULL,
  `soil_type` varchar(100) DEFAULT NULL,
  `slope` varchar(50) DEFAULT NULL,
  `irrigation_type` varchar(100) DEFAULT NULL,
  `location_description` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Crops farm block files table
CREATE TABLE `crops_farm_block_files` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `farm_block_id` int(11) NOT NULL,
  `file_name` varchar(255) NOT NULL,
  `file_path` varchar(500) NOT NULL,
  `file_type` varchar(50) DEFAULT NULL,
  `file_size` int(11) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `uploaded_by` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Livestock farm blocks table
CREATE TABLE `livestock_farm_blocks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `farmer_id` int(11) NOT NULL,
  `block_name` varchar(255) NOT NULL,
  `block_code` varchar(50) DEFAULT NULL,
  `size_hectares` decimal(10,2) DEFAULT NULL,
  `housing_type` varchar(100) DEFAULT NULL,
  `fencing_type` varchar(100) DEFAULT NULL,
  `water_source` varchar(100) DEFAULT NULL,
  `location_description` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Livestock farm data table
CREATE TABLE `livestock_farm_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `farm_block_id` int(11) NOT NULL,
  `livestock_id` int(11) NOT NULL,
  `number_of_animals` int(11) DEFAULT NULL,
  `breeding_stock` int(11) DEFAULT NULL,
  `production_purpose` varchar(255) DEFAULT NULL,
  `feeding_system` varchar(100) DEFAULT NULL,
  `health_status` varchar(100) DEFAULT NULL,
  `record_date` date DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- PRODUCTION DATA TABLES
-- =====================================================

-- Crops farm crops data table
CREATE TABLE `crops_farm_crops_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `farm_block_id` int(11) NOT NULL,
  `crop_id` int(11) NOT NULL,
  `planting_date` date DEFAULT NULL,
  `expected_harvest_date` date DEFAULT NULL,
  `area_planted_hectares` decimal(10,2) DEFAULT NULL,
  `planting_method` varchar(100) DEFAULT NULL,
  `seed_source` varchar(255) DEFAULT NULL,
  `plant_population` int(11) DEFAULT NULL,
  `growth_stage` varchar(100) DEFAULT NULL,
  `record_date` date DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Crops farm disease data table
CREATE TABLE `crops_farm_disease_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `farm_block_id` int(11) NOT NULL,
  `crop_id` int(11) NOT NULL,
  `infection_id` int(11) NOT NULL,
  `severity_level` enum('low','medium','high','severe') DEFAULT NULL,
  `affected_area_percentage` decimal(5,2) DEFAULT NULL,
  `symptoms_observed` text DEFAULT NULL,
  `treatment_applied` text DEFAULT NULL,
  `treatment_date` date DEFAULT NULL,
  `record_date` date DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Crops farm fertilizer data table
CREATE TABLE `crops_farm_fertilizer_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `farm_block_id` int(11) NOT NULL,
  `crop_id` int(11) NOT NULL,
  `fertilizer_id` int(11) NOT NULL,
  `application_date` date DEFAULT NULL,
  `quantity_applied_kg` decimal(10,2) DEFAULT NULL,
  `application_method` varchar(255) DEFAULT NULL,
  `cost_per_kg` decimal(10,2) DEFAULT NULL,
  `total_cost` decimal(10,2) DEFAULT NULL,
  `record_date` date DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Crops farm harvest data table
CREATE TABLE `crops_farm_harvest_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `farm_block_id` int(11) NOT NULL,
  `crop_id` int(11) NOT NULL,
  `harvest_date` date DEFAULT NULL,
  `quantity_harvested_kg` decimal(10,2) DEFAULT NULL,
  `quality_grade` varchar(50) DEFAULT NULL,
  `post_harvest_losses_kg` decimal(10,2) DEFAULT NULL,
  `storage_method` varchar(255) DEFAULT NULL,
  `market_price_per_kg` decimal(10,2) DEFAULT NULL,
  `total_value` decimal(10,2) DEFAULT NULL,
  `record_date` date DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Crops farm marketing data table
CREATE TABLE `crops_farm_marketing_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `farm_block_id` int(11) NOT NULL,
  `crop_id` int(11) NOT NULL,
  `buyer_id` int(11) DEFAULT NULL,
  `sale_date` date DEFAULT NULL,
  `quantity_sold_kg` decimal(10,2) DEFAULT NULL,
  `price_per_kg` decimal(10,2) DEFAULT NULL,
  `total_revenue` decimal(10,2) DEFAULT NULL,
  `transport_cost` decimal(10,2) DEFAULT NULL,
  `market_location` varchar(255) DEFAULT NULL,
  `payment_method` varchar(100) DEFAULT NULL,
  `record_date` date DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Crops farm pesticides data table
CREATE TABLE `crops_farm_pesticides_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `farm_block_id` int(11) NOT NULL,
  `crop_id` int(11) NOT NULL,
  `pesticide_id` int(11) NOT NULL,
  `application_date` date DEFAULT NULL,
  `quantity_applied_liters` decimal(10,2) DEFAULT NULL,
  `target_pest` varchar(255) DEFAULT NULL,
  `application_method` varchar(255) DEFAULT NULL,
  `cost_per_liter` decimal(10,2) DEFAULT NULL,
  `total_cost` decimal(10,2) DEFAULT NULL,
  `effectiveness_rating` enum('poor','fair','good','excellent') DEFAULT NULL,
  `record_date` date DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Crops farm tools table
CREATE TABLE `crops_farm_tools` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `farm_block_id` int(11) NOT NULL,
  `tool_name` varchar(255) NOT NULL,
  `tool_type` varchar(100) DEFAULT NULL,
  `purchase_date` date DEFAULT NULL,
  `purchase_cost` decimal(10,2) DEFAULT NULL,
  `condition_status` enum('new','good','fair','poor','broken') DEFAULT NULL,
  `maintenance_date` date DEFAULT NULL,
  `maintenance_cost` decimal(10,2) DEFAULT NULL,
  `record_date` date DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Livestock production data table
CREATE TABLE `livestock_production_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `farm_block_id` int(11) NOT NULL,
  `livestock_id` int(11) NOT NULL,
  `production_type` enum('milk','eggs','meat','offspring') DEFAULT NULL,
  `quantity_produced` decimal(10,2) DEFAULT NULL,
  `unit_of_measure` varchar(50) DEFAULT NULL,
  `production_date` date DEFAULT NULL,
  `quality_grade` varchar(50) DEFAULT NULL,
  `market_price_per_unit` decimal(10,2) DEFAULT NULL,
  `total_value` decimal(10,2) DEFAULT NULL,
  `record_date` date DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- BUSINESS/MARKET TABLES
-- =====================================================

-- Crop buyers table
CREATE TABLE `crop_buyers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `buyer_name` varchar(255) NOT NULL,
  `buyer_type` enum('individual','company','cooperative','government') DEFAULT NULL,
  `contact_person` varchar(255) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `district_id` int(11) DEFAULT NULL,
  `crops_purchased` text DEFAULT NULL,
  `payment_terms` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Crop processors table
CREATE TABLE `crop_processors` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `processor_name` varchar(255) NOT NULL,
  `processor_type` enum('mill','factory','cooperative','individual') DEFAULT NULL,
  `contact_person` varchar(255) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `district_id` int(11) DEFAULT NULL,
  `crops_processed` text DEFAULT NULL,
  `processing_capacity` varchar(255) DEFAULT NULL,
  `services_offered` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Inputs table
CREATE TABLE `inputs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `input_name` varchar(255) NOT NULL,
  `input_type` enum('seed','fertilizer','pesticide','tool','equipment') DEFAULT NULL,
  `supplier_name` varchar(255) DEFAULT NULL,
  `unit_of_measure` varchar(50) DEFAULT NULL,
  `unit_price` decimal(10,2) DEFAULT NULL,
  `availability_status` enum('available','limited','out_of_stock') DEFAULT NULL,
  `quality_grade` varchar(50) DEFAULT NULL,
  `district_id` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- ADMINISTRATIVE/OPERATIONAL TABLES
-- =====================================================

-- Climate focus table
CREATE TABLE `climate_focus` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `focus_area` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `priority_level` enum('low','medium','high','critical') DEFAULT NULL,
  `target_crops` text DEFAULT NULL,
  `adaptation_strategies` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Government structure table
CREATE TABLE `gov_structure` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `level_name` varchar(255) NOT NULL,
  `parent_id` int(11) DEFAULT NULL,
  `level_type` enum('national','provincial','district','llg','ward','village') DEFAULT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Groupings table
CREATE TABLE `groupings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `group_name` varchar(255) NOT NULL,
  `group_type` varchar(100) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `parent_group_id` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Exercises table
CREATE TABLE `exercises` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `exercise_name` varchar(255) NOT NULL,
  `exercise_type` varchar(100) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `status` enum('planned','ongoing','completed','cancelled') DEFAULT NULL,
  `target_participants` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Exercise officers table
CREATE TABLE `exercise_officers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `exercise_id` int(11) NOT NULL,
  `officer_id` int(11) NOT NULL,
  `role` varchar(100) DEFAULT NULL,
  `assigned_date` date DEFAULT NULL,
  `status` enum('assigned','active','completed','removed') DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Trainings table
CREATE TABLE `trainings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `training_title` varchar(255) NOT NULL,
  `training_type` varchar(100) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `trainer_name` varchar(255) DEFAULT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `location` varchar(255) DEFAULT NULL,
  `target_audience` varchar(255) DEFAULT NULL,
  `max_participants` int(11) DEFAULT NULL,
  `status` enum('planned','ongoing','completed','cancelled') DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Workplan infrastructure activities table
CREATE TABLE `workplan_infrastructure_activities` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `activity_name` varchar(255) NOT NULL,
  `activity_type` varchar(100) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `planned_start_date` date DEFAULT NULL,
  `planned_end_date` date DEFAULT NULL,
  `actual_start_date` date DEFAULT NULL,
  `actual_end_date` date DEFAULT NULL,
  `budget_allocated` decimal(15,2) DEFAULT NULL,
  `budget_spent` decimal(15,2) DEFAULT NULL,
  `status` enum('planned','ongoing','completed','cancelled','delayed') DEFAULT NULL,
  `district_id` int(11) DEFAULT NULL,
  `responsible_officer_id` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- DOCUMENT MANAGEMENT TABLES
-- =====================================================

-- Documents folder table
CREATE TABLE `documents_folder` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `folder_name` varchar(255) NOT NULL,
  `parent_folder_id` int(11) DEFAULT NULL,
  `folder_path` varchar(500) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Document files table
CREATE TABLE `document_files` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `folder_id` int(11) DEFAULT NULL,
  `file_name` varchar(255) NOT NULL,
  `original_name` varchar(255) DEFAULT NULL,
  `file_path` varchar(500) NOT NULL,
  `file_type` varchar(50) DEFAULT NULL,
  `file_size` int(11) DEFAULT NULL,
  `mime_type` varchar(100) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `uploaded_by` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- SECURITY AND PERMISSIONS TABLES
-- =====================================================

-- Permissions items table
CREATE TABLE `permissions_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `permission_name` varchar(255) NOT NULL,
  `permission_code` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `module` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `permission_code` (`permission_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Permissions sets table
CREATE TABLE `permissions_sets` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `set_name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `permissions` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Permissions user districts table
CREATE TABLE `permissions_user_districts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `district_id` int(11) NOT NULL,
  `permission_set_id` int(11) DEFAULT NULL,
  `granted_by` int(11) DEFAULT NULL,
  `granted_date` date DEFAULT NULL,
  `status` enum('active','inactive','revoked') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- SYSTEM TABLES
-- =====================================================

-- Settings table
CREATE TABLE `settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(255) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `setting_type` varchar(50) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `is_system` tinyint(1) DEFAULT 0,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `setting_key` (`setting_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- ADDITIONAL SAMPLE DATA FOR FARM BLOCKS AND PRODUCTION
-- =====================================================

-- Insert sample LLGs
INSERT INTO `adx_llg` (`district_id`, `name`, `code`) VALUES
(1, 'Daru Urban LLG', 'DU'),
(1, 'Daru Rural LLG', 'DR'),
(2, 'Kiunga Urban LLG', 'KU'),
(2, 'Kiunga Rural LLG', 'KR'),
(3, 'North Fly LLG', 'NF'),
(4, 'South Fly LLG', 'SF'),
(5, 'Kerema Urban LLG', 'KEU'),
(5, 'Kerema Rural LLG', 'KER'),
(6, 'Kikori LLG', 'KIK'),
(7, 'Abau LLG', 'ABA');

-- Insert sample wards
INSERT INTO `adx_ward` (`llg_id`, `name`, `code`) VALUES
(1, 'Daru Ward 1', 'DW1'),
(1, 'Daru Ward 2', 'DW2'),
(2, 'Daru Rural Ward 1', 'DRW1'),
(2, 'Daru Rural Ward 2', 'DRW2'),
(3, 'Kiunga Ward 1', 'KW1'),
(3, 'Kiunga Ward 2', 'KW2'),
(4, 'Kiunga Rural Ward 1', 'KRW1'),
(4, 'Kiunga Rural Ward 2', 'KRW2'),
(5, 'North Fly Ward 1', 'NFW1'),
(5, 'North Fly Ward 2', 'NFW2');

-- Insert sample farm blocks
INSERT INTO `crops_farm_blocks` (`farmer_id`, `block_name`, `block_code`, `size_hectares`, `soil_type`, `slope`, `irrigation_type`, `location_description`) VALUES
(1, 'Main Garden Block', 'MGB001', 1.2, 'Clay Loam', 'Gentle', 'Rain Fed', 'Behind main house, facing east'),
(1, 'Coffee Block', 'CFB001', 0.8, 'Sandy Loam', 'Moderate', 'Rain Fed', 'Hillside plot, good drainage'),
(2, 'Vegetable Garden', 'VGB002', 0.6, 'Loam', 'Flat', 'Irrigation', 'Near water source'),
(2, 'Sweet Potato Block', 'SPB002', 1.0, 'Sandy Loam', 'Gentle', 'Rain Fed', 'Open field area'),
(3, 'Taro Wetland', 'TWB003', 1.5, 'Clay', 'Flat', 'Wetland', 'Natural wetland area'),
(3, 'Banana Grove', 'BGB003', 0.7, 'Loam', 'Gentle', 'Rain Fed', 'Sheltered area near house'),
(4, 'Mixed Crop Block', 'MCB004', 1.0, 'Clay Loam', 'Moderate', 'Rain Fed', 'Main farming area'),
(5, 'Cocoa Block', 'COB005', 2.0, 'Loam', 'Gentle', 'Rain Fed', 'Shaded area under trees');

-- Insert sample livestock farm blocks
INSERT INTO `livestock_farm_blocks` (`farmer_id`, `block_name`, `block_code`, `size_hectares`, `housing_type`, `fencing_type`, `water_source`, `location_description`) VALUES
(1, 'Pig Pen Area', 'PPA001', 0.2, 'Traditional Pen', 'Bamboo Fence', 'Stream', 'Behind house, shaded area'),
(2, 'Chicken Coop', 'CCA002', 0.1, 'Raised Coop', 'Wire Mesh', 'Well Water', 'Near kitchen garden'),
(3, 'Goat Paddock', 'GPA003', 0.5, 'Open Shelter', 'Wire Fence', 'Pond', 'Open grazing area'),
(4, 'Duck Pond Area', 'DPA004', 0.3, 'Open Water', 'Natural Boundary', 'Natural Pond', 'Wetland area'),
(5, 'Mixed Livestock', 'MLA005', 0.8, 'Multiple Shelters', 'Mixed Fencing', 'Stream', 'Integrated farming area');

-- Insert sample crop production data
INSERT INTO `crops_farm_crops_data` (`farm_block_id`, `crop_id`, `planting_date`, `expected_harvest_date`, `area_planted_hectares`, `planting_method`, `seed_source`, `plant_population`, `growth_stage`, `record_date`) VALUES
(1, 1, '2024-01-15', '2024-05-15', 0.8, 'Ridge Planting', 'Own Seed', 2000, 'Mature', '2024-08-01'),
(1, 3, '2024-02-01', '2024-12-01', 0.4, 'Transplanting', 'Purchased Seedlings', 50, 'Fruiting', '2024-08-01'),
(2, 4, '2023-10-01', '2024-06-01', 0.8, 'Direct Seeding', 'Certified Seed', 1600, 'Harvested', '2024-08-01'),
(3, 2, '2024-03-01', '2024-09-01', 0.6, 'Transplanting', 'Local Variety', 1200, 'Growing', '2024-08-01'),
(4, 1, '2024-02-15', '2024-06-15', 1.0, 'Mound Planting', 'Own Seed', 2500, 'Mature', '2024-08-01'),
(5, 2, '2024-01-01', '2024-07-01', 1.5, 'Wetland Planting', 'Traditional Variety', 3000, 'Harvested', '2024-08-01'),
(6, 3, '2023-12-01', '2024-10-01', 0.7, 'Transplanting', 'Improved Variety', 70, 'Fruiting', '2024-08-01'),
(8, 5, '2023-08-01', '2024-08-01', 2.0, 'Direct Planting', 'Grafted Seedlings', 400, 'Mature', '2024-08-01');

-- Insert sample harvest data
INSERT INTO `crops_farm_harvest_data` (`farm_block_id`, `crop_id`, `harvest_date`, `quantity_harvested_kg`, `quality_grade`, `post_harvest_losses_kg`, `storage_method`, `market_price_per_kg`, `total_value`, `record_date`) VALUES
(1, 1, '2024-05-20', 800.00, 'Grade A', 50.00, 'Traditional Storage', 2.50, 1875.00, '2024-05-20'),
(2, 4, '2024-06-15', 120.00, 'Premium', 5.00, 'Drying', 15.00, 1725.00, '2024-06-15'),
(5, 2, '2024-07-10', 1200.00, 'Grade A', 80.00, 'Fresh Sale', 3.00, 3360.00, '2024-07-10'),
(8, 5, '2024-08-05', 300.00, 'Premium', 10.00, 'Fermentation', 25.00, 7250.00, '2024-08-05');

-- Insert sample livestock production data
INSERT INTO `livestock_production_data` (`farm_block_id`, `livestock_id`, `production_type`, `quantity_produced`, `unit_of_measure`, `production_date`, `quality_grade`, `market_price_per_unit`, `total_value`, `record_date`) VALUES
(1, 1, 'meat', 45.00, 'kg', '2024-07-15', 'Grade A', 12.00, 540.00, '2024-07-15'),
(2, 2, 'eggs', 150.00, 'pieces', '2024-08-01', 'Fresh', 0.50, 75.00, '2024-08-01'),
(2, 2, 'meat', 8.00, 'kg', '2024-07-20', 'Grade A', 15.00, 120.00, '2024-07-20'),
(3, 4, 'meat', 25.00, 'kg', '2024-06-30', 'Grade A', 18.00, 450.00, '2024-06-30'),
(4, 3, 'eggs', 80.00, 'pieces', '2024-08-01', 'Fresh', 0.60, 48.00, '2024-08-01');

-- Insert sample crop buyers
INSERT INTO `crop_buyers` (`buyer_name`, `buyer_type`, `contact_person`, `phone`, `email`, `address`, `district_id`, `crops_purchased`, `payment_terms`) VALUES
('Western Province Marketing', 'company', 'John Trader', '+************', '<EMAIL>', 'Daru Town, Western Province', 1, 'Sweet Potato, Taro, Banana', 'Cash on Delivery'),
('Gulf Fresh Produce', 'company', 'Mary Buyer', '+************', '<EMAIL>', 'Kerema Town, Gulf Province', 5, 'Vegetables, Root Crops', '7 Days Credit'),
('PNG Coffee Cooperative', 'cooperative', 'Peter Coffee', '+************', '<EMAIL>', 'Goroka, Eastern Highlands', 6, 'Coffee Beans', '30 Days Credit'),
('Local Market Vendor', 'individual', 'Grace Market', '+675 7123 4567', NULL, 'Daru Market', 1, 'Mixed Vegetables', 'Cash Payment'),
('Central Processing Ltd', 'company', 'David Process', '+************', '<EMAIL>', 'Port Moresby, NCD', 7, 'Cocoa, Coffee', '14 Days Credit');

-- Insert sample settings
INSERT INTO `settings` (`setting_key`, `setting_value`, `setting_type`, `description`, `is_system`) VALUES
('app_name', 'AgriStats PNG', 'string', 'Application Name', 1),
('app_version', '1.0.0', 'string', 'Application Version', 1),
('default_currency', 'PGK', 'string', 'Default Currency Code', 0),
('date_format', 'Y-m-d', 'string', 'Default Date Format', 0),
('records_per_page', '25', 'integer', 'Default Records Per Page', 0),
('backup_frequency', 'daily', 'string', 'Database Backup Frequency', 1),
('max_file_upload_size', '10485760', 'integer', 'Maximum File Upload Size in Bytes', 1),
('allowed_file_types', 'jpg,jpeg,png,pdf,doc,docx,xls,xlsx', 'string', 'Allowed File Upload Types', 0);

-- =====================================================
-- END OF SCHEMA AND DATA
-- =====================================================
